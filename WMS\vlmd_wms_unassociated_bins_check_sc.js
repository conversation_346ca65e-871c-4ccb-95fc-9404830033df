/**
 * @description Check for unassociated bins from last week that still have zero quantity
 * and send email report with CSV attachment
 *
 * </br><b>Schedule:</b> Every Friday
 *
 * @NApiVersion 2.1
 * @NScriptType ScheduledScript
 *
 * <AUTHOR>
 * @module vlmd_wms_unassociated_bins_check_sc
 */
define([
	"require",
	"N/email",
	"N/query",
	"N/file",
	"N/runtime",
	"../Classes/vlmd_custom_error_object"
],(require) => {
        const email = require("N/email");
        const query = require("N/query");
        const file = require("N/file");
		const runtime = require("N/runtime");
		
		const CustomErrorObject = require("../Classes/vlmd_custom_error_object");
		const customErrorObject = new CustomErrorObject();
		
	function execute() {
		try {
			log.audit('Script Started', 'Checking for unassociated bins from last week with zero quantity');

			let sqlQuery = /*sql*/ `
	            SELECT
	                BUILTIN.DF(event_log.custrecord_spl_biel_bin) AS bin,
	                BUILTIN.DF(event_log.custrecord_spl_biel_item) AS item,
	                event_log.custrecord_spl_biel_event_timestamp AS timestamp,
	                event_log.custrecord_spl_biel_event_type AS event_type,
	                COALESCE(item_bin_qty.onhand, 0) AS current_quantity
	            FROM
	                customrecord_spl_bin_item_event_log AS event_log
				LEFT JOIN
					itembinquantity AS item_bin_qty
					ON event_log.custrecord_spl_biel_bin = item_bin_qty.bin
					AND event_log.custrecord_spl_biel_item = item_bin_qty.item
	            WHERE
	                event_log.custrecord_spl_biel_event_type = 'Unassociated' AND
	                event_log.custrecord_spl_biel_event_timestamp BETWEEN
	                    TO_DATE(BUILTIN.RELATIVE_RANGES('LBW','START'), 'MM/DD/YYYY') AND
	                    TO_DATE(BUILTIN.RELATIVE_RANGES('LBW','END'), 'MM/DD/YYYY') 
			`;

			let unassociatedBinsFromLastWeek = query.runSuiteQL({
				query: sqlQuery,
			})?.asMappedResults();

			// Generate CSV file if there are results
			if (unassociatedBinsFromLastWeek && unassociatedBinsFromLastWeek.length > 0) {
				let csvContent = generateCSV(unassociatedBinsFromLastWeek);
				let csvFile = createCSVFile(csvContent);

				sendEmail(csvFile, unassociatedBinsFromLastWeek.length);
			} else {
				log.audit('No Results', 'No unassociated bins with zero quantity found from last week');
			}
		} catch (error) {
			log.error('Script execution failed', error.toString());
			throw error;
		}
	}

	/**
	 * Generate CSV content from query results
	 * @param {Array} data - Array of query results
	 * @returns {string} CSV formatted string
	 */
	function generateCSV(data) {
		// CSV headers
		let csvHeaders = ['Bin ID', 'Item ID', 'Timestamp', 'Event Type', 'Current Quantity'];
		let csvContent = csvHeaders.join(',') + '\n';

		// Add data rows
		data.forEach(row => {
			let csvRow = [
				row.bin || '',
				row.item || '',
				row.timestamp || '',
				row.event_type || '',
				row.current_quantity || '0'
			];

			// Escape any commas or quotes in the data
			csvRow = csvRow.map(field => {
				if (typeof field === 'string' && (field.includes(',') || field.includes('"'))) {
					return `"${field.replace(/"/g, '""')}"`;
				}
				return field;
			});

			csvContent += csvRow.join(',') + '\n';
		});

		return csvContent;
	}

	/**
	 * Create and save CSV file to NetSuite file cabinet
	 * @param {string} csvContent - CSV formatted string
	 * @returns {Object} Created file object
	 */
	function createCSVFile(csvContent) {
		let currentDate = new Date();
		let dateString = currentDate.toISOString().split('T')[0]; // YYYY-MM-DD format

		let csvFile = file.create({
			name: `unassociated_bins_check_${dateString}.csv`,
			fileType: file.Type.CSV,
			contents: csvContent,
			folder: 8451007 // SuiteScripts folder, change this to your preferred folder ID
		});

		let csvFileId = csvFile.save();
		
		log.audit('CSV File Created', `File ID: ${csvFileId}`);

		return csvFile;
	}

	/**
	 * Send email with CSV attachment
	 * @param {number} csvFile - File Object of the CSV file
	 * @param {number} recordCount - Number of records in the report
	 */
	function sendEmail(csvFile, recordCount) {
		try {
			let recipients = ['<EMAIL>'];
			let subject = 'Unassociated Bins Check Report';
			let body = `Please find attached report for unassociated bins from last week that still have zero quantity today.\n\nTotal records found: ${recordCount}`;

			email.send({
				author: 342408,
				recipients: recipients,
				subject: subject,
				body: body
			});

			log.audit('Email sent successfully', `Recipients: ${recipients.join(', ')}, Records: ${recordCount}`);
		} catch (error) {
			log.error('Failed to send email', error.toString());
		}
	}

	return {
		execute
	};
});
